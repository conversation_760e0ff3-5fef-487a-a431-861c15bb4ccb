<template>
    <div class="assess-home">
        <div class="content">
            <div class="placeholder">
                <i class="pi pi-chart-line text-4xl text-red-500"></i>
                <p>考试功能开发中...</p>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';

onMounted(() => {
    console.log('考试首页已加载');
});
</script>

<style lang="scss" scoped>
.assess-home {
    padding: 20px 16px;
    height: 100%;
}

.content {
    text-align: center;
    padding-top: 60px;

    h2 {
        font-size: 20px;
        margin-bottom: 12px;
        color: #333;
    }

    p {
        color: #666;
        margin-bottom: 40px;
    }
}

.placeholder {
    background: white;

    i {
        margin-bottom: 20px;
    }

    p {
        color: #999;
        font-size: 14px;
    }
}
</style>
