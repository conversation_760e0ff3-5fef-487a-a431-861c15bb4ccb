<template>
    <!-- iframe 查看器 -->
    <IframeViewer
        v-if="showIframe"
        :url="currentCourseUrl"
        :title="currentCourseTitle"
        @back="handleIframeBack"
        @load="handleIframeLoad"
        @error="handleIframeError"
    />

    <!-- 主页面内容 -->
    <div v-else class="trainer-main">
        <!-- 顶部搜索栏 -->
        <div class="header-section">
            <div class="search-container">
                <div class="search-box">
                    <i class="pi pi-search search-icon"></i>
                    <input
                        type="text"
                        placeholder="查询课程"
                        v-model="searchText"
                        class="search-input"
                    />
                </div>
                <div class="message-icon" @click="handleMessageClick">
                    <i class="pi pi-envelope"></i>
                    <span class="message-dot" v-if="hasNewMessage"></span>
                </div>
            </div>
        </div>

        <!-- 导航栏 -->
        <div class="navigation-section">
            <div class="nav-tabs">
                <div
                    v-for="tab in navTabs"
                    :key="tab.key"
                    class="nav-tab"
                    :class="{ active: activeTab === tab.key }"
                    @click="handleTabChange(tab.key)"
                >
                    <span class="tab-text">{{ tab.label }}</span>
                    <div class="tab-underline" v-if="activeTab === tab.key"></div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="content-section">
            <!-- 轮播图区域 -->
            <div class="banner-section">
                <swiper
                    :modules="[Autoplay, Pagination, EffectFade]"
                    :slides-per-view="1"
                    :space-between="0"
                    :loop="true"
                    :autoplay="{
                        delay: 3000,
                        disableOnInteraction: false
                    }"
                    :pagination="{ clickable: true }"
                    :effect="'fade'"
                    class="swiper-container"
                >
                    <swiper-slide v-for="(banner, index) in banners" :key="index">
                        <img :src="banner.image" :alt="banner.title" class="banner-image" />
                        <div class="banner-content">
                            <h3 class="banner-title">{{ banner.title }}</h3>
                            <p class="banner-subtitle">{{ banner.subtitle }}</p>
                            <div class="banner-tag" v-if="banner.tag">
                                <span class="tag-text">{{ banner.tag }}</span>
                                <span class="tag-hot">🔥</span>
                            </div>
                        </div>
                    </swiper-slide>
                </swiper>
            </div>

            <!-- 课程分类卡片 -->
            <CategorySection :categories="courseCategories" @categoryClick="handleCategoryClick" />

            <!-- 根据当前激活的标签显示不同内容 -->
            <div class="tab-content">
                <!-- 推荐课程组件 -->
                <div v-if="activeTab === 'recommend'" class="recommend-courses-section">
                    <RecommendCourses
                        @viewMore="handleRecommendViewMore"
                        @courseClick="handleRecommendCourseClick"
                    />
                </div>
                <div v-else-if="activeTab === 'free'" class="free-content">
                    <h3>免费课程</h3>
                    <p>这里显示免费课程列表</p>
                </div>
                <div v-else-if="activeTab === 'practice'" class="practice-content">
                    <h3>实战课程</h3>
                    <p>这里显示实战课程列表</p>
                </div>
                <div v-else-if="activeTab === 'live'" class="live-content">
                    <h3>直播专区</h3>
                    <p>这里显示直播列表</p>
                </div>
                <div v-else-if="activeTab === 'news'" class="news-content">
                    <h3>教程内容</h3>
                    <p>这里显示教程列表</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
// 引入 Swiper
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Autoplay, Pagination, EffectFade } from 'swiper/modules';
import RecommendCourses from './courese/Recommend.vue';
import CategorySection from './courese/CategorySection.vue';
import IframeViewer from '@/views/smart-trainer/pages/learn/components/IframeViewer.vue';

// 引入 Swiper 样式
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';

const router = useRouter();

// 响应式状态
const searchText = ref('');
const hasNewMessage = ref(true);
const activeTab = ref('recommend');

// iframe 相关状态
const showIframe = ref(false);
const currentCourseUrl = ref('');
const currentCourseTitle = ref('');

// 导航标签配置
const navTabs = ref([
    { key: 'recommend', label: '推荐' },
    { key: 'free', label: '免费课' },
    { key: 'practice', label: '实战课' },
    { key: 'live', label: '直播专区' },
    { key: 'news', label: '资讯专区' }
]);

// 轮播图数据
const banners = ref([
    {
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/swiper.png',
        title: 'AI Agent+之家智能学习平台—重磅升级',
        subtitle: '学、练、考、评” 全链路闭环覆盖',
        tag: ''
    },
    {
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/sales-ai-trainer/portal/swiper.png',
        title: '全方位学习训练营',
        subtitle: '一体化知识赋能，进阶无阻，高效直达',
        tag: ''
    }
]);

// 课程分类数据
const courseCategories = ref([
    {
        key: 'learn',
        title: '学',
        subtitle: '全面覆盖构建体系',
        introColor: '#E93E43',
        features: ['多元课程', '以学拓知'],
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/main/learn.png',
        backgroundGradient: 'linear-gradient(135deg, #fdebee 0%, #f8d7da 50%, #f5c2c7 100%)'
    },
    {
        key: 'practice',
        title: '练',
        subtitle: '快速上手提升技能',
        introColor: '#FCA43D',
        features: ['真实场景', '以练验学'],
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/main/practice2.png',
        backgroundGradient: 'linear-gradient(135deg, #fff9f1 0%, #fff3cd 50%, #ffeaa7 100%)'
    },
    {
        key: 'assess',
        title: '考',
        subtitle: '专业认证检验能力',
        introColor: '#52C41A',
        features: ['权威认证', '以考验能'],
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/main/access2.png',
        backgroundGradient: 'linear-gradient(135deg, #f6ffed 0%, #d4f1c5 50%, #b7e4c7 100%)'
    },
    {
        key: 'apply',
        title: '评',
        subtitle: '精准画像校准方向',
        introColor: '#1890FF',
        features: ['多维反馈', '以评促升'],
        image: 'https://z.autoimg.cn/dealer_microfe_aidev/assets/chat-knowledge-h5/smart-trainer/main/apply.png',
        backgroundGradient: 'linear-gradient(135deg, #f0f9ff 0%, #cfe2ff 50%, #9ec5fe 100%)'
    }
]);

/**
 * 处理消息图标点击
 */
const handleMessageClick = () => {
    console.log('打开消息页面');
    // 这里可以路由到消息页面
};

/**
 * 处理标签切换
 * @param {string} tabKey - 标签键值
 */
const handleTabChange = tabKey => {
    activeTab.value = tabKey;
    console.log(`切换到标签: ${tabKey}`);
};

/**
 * 处理课程分类点击
 * @param {string} categoryKey - 分类键值
 */
const handleCategoryClick = categoryKey => {
    console.log(`点击了课程分类: ${categoryKey}`);
    // 根据分类键值跳转到对应的页面
    switch (categoryKey) {
        case 'learn':
            router.push('/smart-trainer/learn/home');
            break;
        case 'practice':
            router.push('/smart-trainer/practice/home');
            break;
        case 'assess':
            router.push('/smart-trainer/assess/home');
            break;
        case 'apply':
            router.push('/smart-trainer/apply');
            break;
        default:
            console.log('未知的分类键值:', categoryKey);
    }
};

/**
 * 处理推荐课程查看更多点击事件
 */
const handleRecommendViewMore = () => {
    console.log('推荐课程查看更多，跳转到学习页面');
    router.push('/smart-trainer/learn/home');
};

/**
 * 处理推荐课程点击事件，使用 iframe 打开课程链接
 * @param {Object} course - 课程对象
 */
const handleRecommendCourseClick = course => {
    console.log('推荐课程点击:', course.name);

    // 验证目标URL是否存在
    if (!course.targetUrl) {
        console.warn('课程缺少目标链接:', course.name);
        return;
    }

    try {
        // 验证URL格式
        new URL(course.targetUrl);

        // 设置 iframe 相关数据并显示
        currentCourseUrl.value = course.targetUrl;
        currentCourseTitle.value = course.name;
        showIframe.value = true;
    } catch (error) {
        console.error('无效的课程链接:', course.targetUrl, error);
        // 可以在这里添加用户提示，比如 Toast 消息
    }
};

/**
 * 处理 iframe 返回事件
 */
const handleIframeBack = () => {
    showIframe.value = false;
    currentCourseUrl.value = '';
    currentCourseTitle.value = '';
};

/**
 * 处理 iframe 加载完成事件
 */
const handleIframeLoad = () => {
    console.log('iframe 加载完成');
};

/**
 * 处理 iframe 加载错误事件
 */
const handleIframeError = error => {
    console.error('iframe 加载错误:', error);
    // 可以在这里添加错误处理逻辑，比如显示错误提示
};

// 生命周期
onMounted(() => {
    // 组件挂载后的逻辑
});

onUnmounted(() => {
    // 清理定时器等资源
});
</script>

<style lang="scss" scoped>
.trainer-main {
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    // 顶部搜索栏
    .header-section {
        background: #fff;
        padding: 6px 16px 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        .search-container {
            display: flex;
            align-items: center;
            gap: 12px;

            .search-box {
                flex: 1;
                position: relative;
                display: flex;
                align-items: center;
                background: #f8f9fa;
                border-radius: 20px;
                padding: 8px 16px;

                .search-icon {
                    color: #999;
                    margin-right: 8px;
                }

                .search-input {
                    flex: 1;
                    border: none;
                    background: transparent;
                    outline: none;
                    font-size: 14px;
                    color: #333;

                    &::placeholder {
                        color: #999;
                    }
                }
            }

            .message-icon {
                position: relative;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;

                i {
                    font-size: 20px;
                    color: #666;
                }

                .message-dot {
                    position: absolute;
                    top: 8px;
                    right: 8px;
                    width: 8px;
                    height: 8px;
                    background: #ff4757;
                    border-radius: 50%;
                }
            }
        }
    }

    // 导航栏
    .navigation-section {
        background: #fff;
        padding: 0 16px;

        .nav-tabs {
            display: flex;
            align-items: center;
            overflow-x: auto;
            gap: 24px;

            &::-webkit-scrollbar {
                display: none;
            }
            scrollbar-width: none;
            -ms-overflow-style: none;

            .nav-tab {
                position: relative;
                padding: 4px 0;
                cursor: pointer;
                white-space: nowrap;
                transition: color 0.3s ease;

                .tab-text {
                    font-size: 16px;
                    font-weight: 500;
                    color: #000;
                    transition: color 0.3s ease;
                }

                .tab-underline {
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    transform: translateX(-50%);
                    height: 4px;
                    width: 30%;
                    background: #ff4757;
                    border-radius: 4px;
                }

                &.active {
                    .tab-text {
                        color: #ff4757;
                        font-weight: 600;
                    }
                }
            }
        }
    }

    // 主要内容区域
    .content-section {
        padding: 10px 16px;
        flex: 1;
        overflow-y: auto;

        // 轮播图区域
        .banner-section {
            margin-bottom: 10px;

            .swiper-container {
                position: relative;
                border-radius: 6px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                // 调整高度
                height: 120px;

                .swiper-slide {
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    .banner-image {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }

                    .banner-content {
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: linear-gradient(
                            135deg,
                            rgba(29, 78, 216, 0.8),
                            rgba(59, 130, 246, 0.6)
                        );
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        padding: 24px;
                        color: white;

                        .banner-title {
                            font-size: 16px;
                            font-weight: 600;
                            margin-bottom: 8px;
                        }

                        .banner-subtitle {
                            font-size: 14px;
                            opacity: 0.9;
                            margin-bottom: 16px;
                        }

                        .banner-tag {
                            display: flex;
                            align-items: center;
                            gap: 4px;
                            background: rgba(255, 255, 255, 0.2);
                            padding: 4px 12px;
                            border-radius: 16px;
                            width: fit-content;

                            .tag-text {
                                font-size: 12px;
                                font-weight: 500;
                            }
                        }
                    }
                }

                // 覆盖 Swiper 分页器样式
                :deep(.swiper-pagination-bullet) {
                    width: 8px;
                    height: 8px;
                    background: rgba(255, 255, 255, 0.7);
                    opacity: 1;
                    transition: all 0.3s ease;
                }

                :deep(.swiper-pagination-bullet-active) {
                    width: 20px;
                    border-radius: 4px;
                    background: white;
                }
            }
        }
    }
}
</style>
